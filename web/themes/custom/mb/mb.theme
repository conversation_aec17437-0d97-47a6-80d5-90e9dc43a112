<?php

/**
 * @file
 * Functions to support theming.
 */

use Dr<PERSON>al\Component\Utility\Html;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Site\Settings;
use Drupal\Core\Url;
use Dr<PERSON>al\file\Entity\File;
use <PERSON><PERSON><PERSON>\node\NodeInterface;

/**
 * Implements hook_preprocess_HOOK() for html.
 */
function mb_preprocess_html(&$variables) {

  // Add environment indicator.
  $variables['env'] = Settings::get('push_env') ?? 'local';

  // Get the active domain using the domain.negotiator service.
  if (\Drupal::moduleHandler()->moduleExists('domain')) {
    /** @var \Drupal\domain\DomainNegotiatorInterface $domain_negotiator */
    $domain_negotiator = \Drupal::service('domain.negotiator');
    $active_domain = $domain_negotiator->getActiveDomain();

    if ($active_domain) {
      // Add the active domain to the variables.
      $variables['active_domain'] = [
        'id' => $active_domain->id(),
        'name' => $active_domain->label(),
        'default' => $active_domain->isDefault(),
      ];
    }
  }

  $route = \Drupal::routeMatch()->getRouteName();

  $wide = FALSE;

  if (($node = \Drupal::routeMatch()->getParameter('node')) || ($node = \Drupal::routeMatch()->getParameter('node_preview'))) {
    if ($node instanceof NodeInterface) {
      if ($node->getType() == 'page') {
        $wide = TRUE;
      }
    }
  }

  switch ($route) {

    case 'entity.user.canonical':
      $variables['attributes']['class'][] = 'page-user-profile';
      $variables['attributes']['class'][] = 'has-content-cards';
      break;

    case 'entity.user.edit_form':
      $variables['attributes']['class'][] = 'page-user-edit';
      break;

    // Auction page.
    case 'entity.auction.canonical':
      $variables['node_type'] = 'auction';
      $variables['attributes']['class'][] = 'has-content-cards';
      break;

    case 'auctions.user_auctions':
      $variables['attributes']['class'][] = 'page-user-auctions';
      $variables['attributes']['class'][] = 'has-content-cards';
      break;

    case 'view.frontpage.front':
      $variables['attributes']['class'][] = 'has-content-cards';
      break;
  }

  $variables['attributes']['class'][] = $wide ? 'width-wide' : 'width-narrow';
}

/**
 * Implements hook_theme_suggestions_HOOK_alter() for field.
 */
function mb_theme_suggestions_field_alter(array &$suggestions, array $variables) {
  $element = $variables['element'];
  $suggestions[] = 'field__' . $element['#entity_type'] . '__' . $element['#field_name'] . '__' . $element['#bundle'] . '__' . $element['#view_mode'];
}

/**
 * Add twig suggestions for input elements.
 *
 * If a form api element has a data-twig-suggestion attribute, then allow twig
 * theme override, add to suggestions.
 *
 * @param array $suggestions
 *   Current list of twig suggestions.
 * @param array $variables
 *   Every variable of the current element.
 */
function mb_theme_suggestions_input_alter(&$suggestions, array $variables) {
  $element = $variables['element'];

  if (isset($element['#attributes']['data-twig-suggestion'])) {
    $suggestions[] = 'input__' . $element['#type'] . '__' . $element['#attributes']['data-twig-suggestion'];
  }
}

/**
 * Implements hook_preprocess_HOOK() for links__language_block.
 */
function mb_preprocess_links__language_block(&$variables) {
  foreach ($variables['links'] as $i => $link) {
    $linkLanguage = $link['link']['#options']['language'];
    $variables['links'][$i]['link']['#title'] = $linkLanguage->get('id');
  }
}

/**
 * Implements hook_preprocess_HOOK() for file-link.html.twig.
 */
function mb_preprocess_file_link(&$variables) {
  $file = $variables['file'];
  $file_entity = $file instanceof File ? $file : File::load($file->fid);
  $url = \Drupal::service('file_url_generator')->generateAbsoluteString($file_entity->getFileUri());

  // Use the description as the link text if available.
  if (empty($variables['description'])) {
    $link_text = $file_entity
      ->getFilename();
  }
  else {
    $link_text = $variables['description'];
  }

  $variables['title'] = $link_text;
  $variables['url'] = Url::fromUri($url);
  $variables['file_size'] = $file->getSize();
}

/**
 * Implements hook_library_info_alter().
 */
function mb_library_info_alter(&$libraries) {
  if (!empty($libraries['flatpickr-init']['css'])) {
    unset($libraries['flatpickr-init']['css']);
  }
}

/**
 * Implements hook_preprocess_HOOK() for paragraph__accordion_item.
 */
function mb_preprocess_paragraph__accordion_item(&$variables) {
  if (!$variables['paragraph']->get('field_title')->isEmpty()) {
    $variables['content_attributes']['id'] = Html::getId(\Drupal::service('transliteration')->removeDiacritics($variables['paragraph']->get('field_title')->value));
  }
}

/**
 * Implements hook_preprocess_HOOK() for paragraph--banner.html.twig.
 */
function mb_preprocess_paragraph__banner(&$variables) {
  // Get paragraph entity.
  $paragraph = $variables['paragraph'];
  // Check if field_background is not empty and add its value as class.
  if (!$paragraph->get('field_background')->isEmpty()) {
    $variables['attributes']['class'][] = 'bg-' . $paragraph->get('field_background')->value;
    $variables['attributes']['class'][] = 'background';
  }
}

/**
 * Implements hook_preprocess_HOOK() for paragraph__slide.
 */
function mb_preprocess_paragraph__slide(&$variables) {
  if (!$variables['paragraph']->get('field_link')->isEmpty()) {
    $variables['link_to'] = Url::fromUri($variables['paragraph']->get('field_link')->uri)->toString();
  }
}

/**
 * Implements hook_preprocess_HOOK() for field__paragraph__field_blocks__banner.
 */
function mb_preprocess_field__paragraph__field_blocks__banner(&$variables) {
  $variables['attributes']['class'][] = 'grid';
  $variables['attributes']['class'][] = 'grid__' . $variables['element']['#object']->get('field_layout')->value;
}

/**
 * Implements hook_suggestions_HOOK_alter() for fieldset.
 */
function mb_theme_suggestions_fieldset_alter(&$suggestions, array $variables) {
  $element = $variables['element'];
  if (isset($element['#field_name'])) {
    $suggestions[] = 'fieldset__' . $element['#field_name'];
  }
}

/**
 * Implements hook_preprocess_HOOK() for commerce-invoice.
 */
function mb_preprocess_commerce_invoice(&$variables) {
  /** @var \Drupal\commerce_invoice\Entity\InvoiceInterface $invoice */
  $invoice = $variables['invoice_entity'];
  if ($invoice->getBillingProfile()->address->country_code != 'LV') {
    \Drupal::service('auctions.language_switcher')->changeActiveLanguage('en');
    /** @var \Drupal\commerce_order\Entity\OrderItem $item */
    $purchase_entity = $invoice->getOrders()[0]->getItems()[0]->getPurchasedEntity()->getTranslation('en');
    foreach ($invoice->getItems() as $item) {
      $item->set('title', $purchase_entity->label());
    }
    $variables['sum_with_words'] = \Drupal::service('mezabirza.twig_extension')->getEnCurrency($invoice->total_price->number);
    $variables['totals']['adjustments'][0]['label'] = 'VAT';
  }
  else {
    $variables['sum_with_words'] = \Drupal::service('mezabirza.twig_extension')->getLvCurrency($invoice->total_price->number);
  }
}

/**
 * Implements hook_theme_suggestions_HOOK_alter() for suggestions_page.
 */
function mb_theme_suggestions_page_alter(array &$suggestions, array $variables): void {
  // Get Request Object.
  $request = \Drupal::request();

  // If there is HTTP Exception.
  if ($exception = $request->attributes->get('exception')) {
    if ($message = $exception->getMessage()) {
      $suggestions[] = 'page__' . $message;
    }
  }
}

/**
 * Implements hook_preprocess_HOOK() for page--auction-access-denied.html.twig.
 */
function mb_preprocess_page__auction_access_denied(&$variables) {
  $variables['current_url'] = \Drupal::request()->getRequestUri();
}

/**
 * Implements hook_preprocess_HOOK() for page--403.html.twig.
 */
function mb_preprocess_page__403(&$variables) {
  $variables['current_url'] = \Drupal::request()->getRequestUri();
}

/**
 * Implements hook_preprocess_HOOK() for page--404.html.twig.
 */
function mb_preprocess_page__404(&$variables) {
  $variables['current_url'] = \Drupal::request()->getRequestUri();
}

/**
 * Implements hook_theme_suggestions_HOOK_alter() for user.
 */
function mb_theme_suggestions_user_alter(array &$suggestions, array $variables): void {
  $suggestions = [];
  $sanitized_view_mode = strtr($variables['elements']['#view_mode'], '.', '_');
  $suggestions[] = 'user__' . $sanitized_view_mode;
}

/**
 * Implements hook_theme_suggestions_HOOK_alter() for email-wrap.
 */
function mb_theme_suggestions_email_wrap_alter(array &$suggestions, array $variables) {
  // Get the email object
  if (isset($variables['email'])) {
    // Get the recipient email address
    $to = $variables['email']->getTo()[0]->getEmail();

    // Load the user by email
    $user = user_load_by_mail($to);

    // If we have a user and they have a domain set
    if ($user && !$user->get('field_domain')->isEmpty()) {
      // Get the domain ID
      $domain_id = $user->get('field_domain')->target_id;

      // Load the domain entity
      $domain = \Drupal::entityTypeManager()->getStorage('domain')->load($domain_id);

      if ($domain) {
        // Add a suggestion based on the domain ID
        $suggestions[] = 'email_wrap__' . $domain->id();
      }
    }
  }
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function mb_form_user_locations_form_alter(&$form, FormStateInterface $form_state) {
  foreach ($form['field_locations']['widget'] as $key => $value) {
    if (is_numeric($key)) {
      unset($form['field_locations']['widget'][$key]['top']['paragraph_type_title']);
      $form['field_locations']['widget'][$key]['top']['#weight'] = 1000;
    }
  }
}

/**
 * Implements hook_preprocess_email_wrap().
 */
function mb_preprocess_email_wrap(array &$variables) {
  $variables['unsubscribe'] = $variables['email']->getVariables()['unsubscribe'] ?? '';

  // Get the email object
  if (isset($variables['email'])) {
    // Get the recipient email address
    $to = $variables['email']->getTo()[0]->getEmail();

    // Load the user by email
    $user = user_load_by_mail($to);

    // If we have a user and they have a domain set
    if ($user && !$user->get('field_domain')->isEmpty()) {
      // Get the domain ID
      $domain_id = $user->get('field_domain')->target_id;

      // Load the domain entity
      $domain = \Drupal::entityTypeManager()->getStorage('domain')->load($domain_id);

      if ($domain) {
        // Add domain info to variables
        $variables['domain'] = [
          'id' => $domain->id(),
          'name' => $domain->label(),
          'hostname' => $domain->getHostname(),
          'scheme' => $domain->getScheme(),
          'url' => 'https://' . $domain->getHostname(),
          'default' => $domain->isDefault(),
        ];
      }
    }
  }
}

/**
 * Implements hook_preprocess_HOOK() for paragraph--video.html.twig.
 */
function mb_preprocess_paragraph__video(&$variables) {
  $variables['video_url'] = $variables['paragraph']?->get('field_video')?->entity?->createFileUrl();
  $poster = $variables['paragraph']?->get('field_image')?->entity?->uri?->value;
  if ($poster) {
    /** @var \Drupal\image\Entity\ImageStyle $style */
    $style = \Drupal::entityTypeManager()->getStorage('image_style')->load('hero');
    $variables['poster'] = $style?->buildUrl($poster);
  }
}

/**
 * Implements hook_preprocess_HOOK() for paragraph--icon-card.html.twig.
 */
function mb_preprocess_paragraph__icon_card(&$variables) {
  // Get link field from paragraph and add url to variables.
  $link = $variables['paragraph']?->get('field_link')?->uri;
  if ($link) {
    $variables['url'] = Url::fromUri($link)->toString();
  }

}

/**
 * Implements hook_preprocess_HOOK() for node.html.twig.
 */
function mb_preprocess_node(&$variables): void {
  $variables['absolute_url'] = \Drupal::request()->getSchemeAndHttpHost() . \Drupal::request()->getRequestUri();
}

/**
 * Implements hook_preprocess_page().
 */
function mb_preprocess_page(&$variables) {
  // Get the active domain using the domain.negotiator service.
  if (\Drupal::moduleHandler()->moduleExists('domain')) {
    /** @var \Drupal\domain\DomainNegotiatorInterface $domain_negotiator */
    $domain_negotiator = \Drupal::service('domain.negotiator');
    $active_domain = $domain_negotiator->getActiveDomain();

    if ($active_domain) {
      // Add the active domain to the variables.
      $variables['active_domain'] = [
        'id' => $active_domain->id(),
        'name' => $active_domain->label(),
        'default' => $active_domain->isDefault(),
      ];
    }
  }
}

/**
 * Implements hook_preprocess_HOOK() for block--mezabirza-contact.html.twig.
 */
function mb_preprocess_block__mezabirza_contact(&$variables) {
  // Get the active domain using the domain.negotiator service.
  if (\Drupal::moduleHandler()->moduleExists('domain')) {
    /** @var \Drupal\domain\DomainNegotiatorInterface $domain_negotiator */
    $domain_negotiator = \Drupal::service('domain.negotiator');
    $active_domain = $domain_negotiator->getActiveDomain();

    if ($active_domain) {
      // Add the active domain to the variables.
      $variables['active_domain'] = [
        'id' => $active_domain->id(),
        'name' => $active_domain->label(),
        'default' => $active_domain->isDefault(),
      ];
    }
  }
}
