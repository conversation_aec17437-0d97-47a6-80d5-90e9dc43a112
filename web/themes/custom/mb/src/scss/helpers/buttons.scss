@mixin borderline--thin($color: transparent) {
  @include transition-default((background-color, border-color, color));
  border: 0 solid $color;
  box-shadow: inset 0 0 0 0.05em $color;
}

@mixin borderline--thick($color: transparent) {
  border: 1px solid $color;
  box-shadow: inset 0 0 0 0.05em $color;
}

@mixin button(
  $background-color: $color--primary-background,
  $foreground-color: $color--default-text-negative,
  $border-color: $color--primary-background,
  $background-hover-color: $color--default-text-negative,
  $foreground-hover-color: $color--primary-background,
  $border-animation: false,
  $offset-padding: false,
  $fontWeight: 400,
  $fontSize: $font-size--base,
  $text-align: center,
  $round: false
) {
  @include borderline--thin($border-color);

  align-items: center;
  appearance: none;
  background-color: $background-color;
  color: $foreground-color;
  display: inline-flex;
  justify-content: space-between;
  font-family: $font-family--heading;
  margin: .5rem;
  margin-left: 0;
  line-height: 1;
  outline-offset: space(0.25);
  padding: 0.6em 1.5em;
  font-size: $fontSize;
  font-weight: $fontWeight;
  text-decoration: none;
  text-align: $text-align;
  border-radius: 3px;
  &:last-of-type:not(.button--follow) {
    margin-right: 0;
  }

  @if $offset-padding {
    margin: space(-1) space(-1.5);
  }

  @if $round {
    border-radius: space(1.5);
    padding: space(0.33) space(0.74);
    position: relative;

    &::after {
      content: '';
      display: block;
      bottom: -0.75rem;
      left: -0.5rem;
      position: absolute;
      right: -0.5rem;
      top: -0.75rem;
    }
  }

  &__icon {
    display: inline-flex;
    margin-right: space();

    use {
      fill: currentColor;
    }

    &--hidden {
      display: none;
    }
  }

  &--icon-only {
    font-size: $font-size--large-1;

    .button__icon {
      margin: 0;
    }
  }

  &__expanded-text {
    display: none;
  }

  &[aria-expanded='true'] {
    .button__collapsed-text {
      display: none;
    }
    .button__expanded-text {
      display: block;
    }
  }

  &:visited {
    color: $foreground-color;
  }

  &:hover,
  &:focus {
    @if $border-animation {
      @include borderline--thick($border-color);
    } @else {
      @include borderline--thin($border-color);
    }

    background-color: $background-hover-color;
    color: $foreground-hover-color;
  }
}

@mixin button--primary {
  @include button(
    $background-color: $color--primary-background,
    $foreground-color: $color--default-text-negative,
    $border-color: transparent,
    $background-hover-color: $color--primary,
    $foreground-hover-color: $color--default-text-negative,
  );
}

@mixin button--outline {
  @include button(
    $background-color: #fff,
    $foreground-color: $color--primary,
    $border-color: $color--primary,
    $background-hover-color: $color--primary,
    $foreground-hover-color: $color--default-text-negative,
  );
}

@mixin button--outline--small {
  @include button(
    $background-color: #fff,
    $foreground-color: $color--primary,
    $border-color: $color--primary,
    $background-hover-color: $color--primary,
    $foreground-hover-color: $color--default-text-negative,
    $fontSize: $font-size--large-1
  );
}

@mixin button--action {
  @include button(
    $background-color: $color--button-action-background,
    $foreground-color: $color--default-text,
    $border-color: $color--secondary-border,
    $background-hover-color: $color--button-action-background-hover,
    $foreground-hover-color: $color--default-text,
    $fontWeight: normal,
    $fontSize: $font-size--middle
  );
}

@mixin button--cta {
  @include button(
    $background-color: $color--primary,
    $foreground-color: $color--default-text-negative,
    $border-color: $color--secondary-border,
    $background-hover-color: $color--primary-background,
    $foreground-hover-color: $color--default-text-negative,
    $fontSize: $font-size--middle
  );
}

@mixin button--cta--small {
  @include button(
    $background-color: $color--primary,
    $foreground-color: $color--default-text-negative,
    $border-color: $color--secondary-border,
    $background-hover-color: $color--primary-background,
    $foreground-hover-color: $color--default-text-negative,
    $fontSize: $font-size--large-1
  );
}

@mixin button--secondary {
  @include button(
    $background-color: $color--secondary-action,
    $foreground-color: $color--default-text,
    $border-color: $color--secondary-border,
    $background-hover-color: $color--secondary-action-dark,
    $foreground-hover-color: $color--default-text,
    $fontSize: $font-size--middle
  );
}

@mixin button--secondary--small {
  @include button(
    $background-color: $color--secondary-action,
    $foreground-color: $color--default-text,
    $border-color: $color--secondary-border,
    $background-hover-color: $color--secondary-action-dark,
    $foreground-hover-color: $color--default-text,
    $fontSize: $font-size--large-1
  );
}

@mixin button--danger {
  @include button(
    $background-color: $color--error,
    $foreground-color: $color--default-text-negative,
    $border-color: transparent,
    $background-hover-color: $color--error-dark,
    $foreground-hover-color: $color--default-text-negative
  );
}

@mixin button--danger--small {
  @include button(
    $background-color: $color--error,
    $foreground-color: $color--default-text-negative,
    $border-color: transparent,
    $background-hover-color: $color--error-dark,
    $foreground-hover-color: $color--default-text-negative,
    $fontSize: $font-size--large-1
  );
}

@mixin button--clean {
  @include button(
    $background-color: transparent,
    $foreground-color: $color--default-text,
    $border-color: #8080804f,
    $background-hover-color: rgba(177, 177, 177, 0.10),
    $foreground-hover-color: $color--default-text
  );
}
@mixin button--clean--small {
  @include button(
    $background-color: transparent,
    $foreground-color: $color--default-text,
    $border-color: #8080804f,
    $background-hover-color: rgba(177, 177, 177, 0.10),
    $foreground-hover-color: $color--default-text,
    $fontSize: $font-size--large-1
  );
}
@mixin button--auto {
  @include button(
    $background-color: #f0bb81,
    $foreground-color: $color--default-text,
    $border-color: #8080804f,
    $background-hover-color: #f3c08e,
    $foreground-hover-color: $color--default-text
  );
}
@mixin button--auto--small {
  @include button(
    $background-color: #f0bb81,
    $foreground-color: $color--default-text,
    $border-color: #8080804f,
    $background-hover-color: #f3c08e,
    $foreground-hover-color: $color--default-text,
    $fontSize: $font-size--large-1
  );
}
